# User Service API Integration Checklist

## Overview
This checklist provides a comprehensive plan for integrating all User Service API endpoints into the Flutter application. The API includes user management, follow system, friendship system, blocking functionality, and user relationships.

**Base URL**: `https://api-dev.toii.social/user/api/v1`
**Authentication**: Bearer token required for all endpoints

## Priority Levels
- 🔴 **High Priority**: Core user functionality (user management, basic follow/friend operations)
- 🟡 **Medium Priority**: Advanced features (batch operations, detailed stats)
- 🟢 **Low Priority**: Nice-to-have features (advanced filtering, metadata handling)

---

## Phase 1: Core Infrastructure Setup 🔴

### 1.1 Base Models and Response Structures
- [ ] Create `lib/model/user_service/` directory structure
- [ ] Implement `CommonResponse` model matching API response format
- [ ] Implement `ErrorDetail` model for error handling
- [ ] Implement `ResultResponse` model for simple success/failure responses
- [ ] Implement `JSONBMap` typedef for metadata handling
- [ ] Create pagination models (`has_more`, `next_cursor` pattern)

### 1.2 User Models
- [ ] Create `UserInfoResponse` model (comprehensive user data)
- [ ] Create `UserProfileResponse` model (profile view with relationship status)
- [ ] Create `UsernameAvailabilityResponse` model
- [ ] Create `CreateUserRequest` and `UpdateUserRequest` models
- [ ] Create `UploadAvatarRequest` model
- [ ] Add user role and status enums (`UserRole`, `UserStatus`)

### 1.3 Service Layer Setup
- [ ] Create `lib/core/service/user_service.dart` using Retrofit
- [ ] Configure Dio interceptors for User Service endpoints
- [ ] Add authentication headers handling
- [ ] Implement proper error handling and response parsing
- [ ] Add logging for API calls

### 1.4 Repository Layer Setup
- [ ] Create `lib/core/repository/user_service_repository.dart` (abstract)
- [ ] Create `UserServiceRepositoryImpl` implementation
- [ ] Add dependency injection in `lib/locator/locator.dart`
- [ ] Implement error handling and data transformation

---

## Phase 2: User Management API Integration 🔴

### 2.1 User CRUD Operations
- [ ] **POST** `/users` - Create user
  - [ ] Service method implementation
  - [ ] Repository method implementation
  - [ ] Request/response model integration
- [ ] **GET** `/users/me` - Get current user profile
- [ ] **PUT** `/users/me` - Update current user profile
- [ ] **DELETE** `/users/me` - Delete current user account

### 2.2 User Profile and Avatar
- [ ] **GET** `/users/{id}` - Get user profile by ID
- [ ] **POST** `/users/me/avatar` - Upload user avatar
  - [ ] Handle file upload with proper content type
  - [ ] Implement image compression if needed
- [ ] **GET** `/users/username/{username}/availability` - Check username availability

### 2.3 User Search and Discovery
- [ ] **GET** `/users/search` - Search users
  - [ ] Implement query parameters (q, limit, cursor)
  - [ ] Add pagination support
  - [ ] Handle empty results gracefully

---

## Phase 3: Follow System API Integration 🔴

### 3.1 Follow Models
- [ ] Create `FollowUserRequest` model
- [ ] Create `FollowResponse` model with user details
- [ ] Create `FollowsResponse` model for paginated lists
- [ ] Create `FollowingListResponse` model
- [ ] Add `FollowStatus` enum (active, muted, blocked, removed)
- [ ] Create `FollowStats` model

### 3.2 Follow Operations
- [ ] **POST** `/follows` - Follow user
  - [ ] Handle conflict responses (already following)
  - [ ] Implement optimistic updates
- [ ] **DELETE** `/follows/{followed_id}` - Unfollow user
  - [ ] Handle not found responses gracefully

### 3.3 Follow Lists and Statistics
- [ ] **GET** `/follows/followers` - Get followers list
  - [ ] Implement pagination (limit, cursor)
  - [ ] Add status filtering
- [ ] **GET** `/follows/following` - Get following list
  - [ ] Implement pagination and filtering
- [ ] **GET** `/users/{id}/followers` - Get user's followers
- [ ] **GET** `/users/{id}/following` - Get user's following
- [ ] **GET** `/users/{id}/follow-stats` - Get follow statistics

---

## Phase 4: Friendship System API Integration 🟡

### 4.1 Friendship Models
- [ ] Create `SendFriendRequestRequest` model
- [ ] Create `FriendRequestResponse` model
- [ ] Create `FriendRequestsResponse` model for pagination
- [ ] Create `FriendshipResponse` model
- [ ] Create `FriendshipsResponse` model
- [ ] Create `FriendsListResponse` model
- [ ] Add `FriendRequestStatus` enum (pending, accepted, declined, cancelled, blocked)
- [ ] Add `FriendshipStatus` enum (active, pending, blocked, removed)

### 4.2 Friend Request Operations
- [ ] **POST** `/friendships/requests` - Send friend request
  - [ ] Handle duplicate request conflicts
  - [ ] Include optional message parameter
- [ ] **GET** `/friendships/requests` - Get received friend requests
  - [ ] Implement status filtering and pagination
- [ ] **PUT** `/friendships/requests/{id}/accept` - Accept friend request
- [ ] **PUT** `/friendships/requests/{id}/decline` - Decline friend request
- [ ] **DELETE** `/friendships/requests/{id}` - Cancel friend request

### 4.3 Friendship Management
- [ ] **GET** `/friendships` - Get friendships
  - [ ] Implement status filtering and pagination
- [ ] **DELETE** `/friendships/{friend_id}` - Remove friendship
- [ ] **GET** `/users/{id}/friends` - Get user's friends list
- [ ] **POST** `/users/friends-list-batch` - Get multiple users' friends lists

---

## Phase 5: Blocking System API Integration 🟡

### 5.1 Blocking Models
- [ ] Create `BlockUserRequest` model
- [ ] Create `BlockedUserResponse` model
- [ ] Create `BlockedUsersResponse` model for pagination

### 5.2 Blocking Operations
- [ ] **POST** `/blocking/{user_id}` - Block user
  - [ ] Handle metadata parameter
  - [ ] Implement proper conflict handling
- [ ] **DELETE** `/blocking/{user_id}` - Unblock user
- [ ] **GET** `/users/me/blocked` - Get blocked users list
  - [ ] Implement pagination support

---

## Phase 6: User Relationships and Advanced Features 🟡

### 6.1 Relationship Models
- [ ] Create `CheckUserRelationshipsRequest` model
- [ ] Create `UserRelationshipsResponse` model
- [ ] Create `GetFriendsListBatchRequest` model
- [ ] Create `FriendsListBatchResponse` model

### 6.2 Relationship Operations
- [ ] **POST** `/users/relationships/check` - Check user relationships
  - [ ] Handle batch user ID checking
  - [ ] Optimize for performance with large lists
- [ ] **GET** `/users/{id}/relationships` - Get user relationships

---

## Phase 7: State Management and UI Integration 🔴

### 7.1 Cubit Classes
- [ ] Create `UserManagementCubit` and `UserManagementState`
  - [ ] Handle user profile operations
  - [ ] Manage avatar upload state
  - [ ] Handle username availability checking
- [ ] Create `FollowCubit` and `FollowState`
  - [ ] Manage follow/unfollow operations
  - [ ] Handle followers/following lists with pagination
  - [ ] Implement optimistic updates
- [ ] Create `FriendshipCubit` and `FriendshipState`
  - [ ] Manage friend request operations
  - [ ] Handle friendship status changes
  - [ ] Manage friends lists
- [ ] Create `BlockingCubit` and `BlockingState`
  - [ ] Handle block/unblock operations
  - [ ] Manage blocked users list
- [ ] Create `UserRelationshipsCubit` and `UserRelationshipsState`
  - [ ] Handle relationship checking
  - [ ] Manage batch operations

### 7.2 Error Handling and Loading States
- [ ] Implement consistent error handling across all Cubits
- [ ] Add proper loading states for all operations
- [ ] Handle network connectivity issues
- [ ] Implement retry mechanisms for failed requests
- [ ] Add proper error messages for user feedback

### 7.3 Dependency Injection
- [ ] Register all new services in `lib/locator/locator.dart`
- [ ] Register all new repositories
- [ ] Register all new Cubits
- [ ] Ensure proper dependency resolution

---

## Phase 8: UI Integration and User Experience 🟡

### 8.1 Profile Management UI
- [ ] Update user profile screens to use new API
- [ ] Implement avatar upload functionality
- [ ] Add username availability checking in real-time
- [ ] Handle profile update operations

### 8.2 Social Features UI
- [ ] Update follow/unfollow buttons with new API
- [ ] Implement followers/following lists with pagination
- [ ] Add friend request management UI
- [ ] Implement blocking/unblocking functionality
- [ ] Add user search functionality

### 8.3 Relationship Status Display
- [ ] Show relationship status in user profiles
- [ ] Display follow/friendship status consistently
- [ ] Handle blocked user interactions properly
- [ ] Implement proper privacy controls

---

## Phase 9: Testing and Quality Assurance 🟢

### 9.1 Unit Tests
- [ ] Write tests for all service classes
- [ ] Write tests for all repository implementations
- [ ] Write tests for all model serialization/deserialization
- [ ] Write tests for all Cubit classes and state management

### 9.2 Integration Tests
- [ ] Test API integration with mock responses
- [ ] Test error handling scenarios
- [ ] Test pagination functionality
- [ ] Test authentication and authorization

### 9.3 Performance and Optimization
- [ ] Optimize API calls to reduce redundant requests
- [ ] Implement proper caching strategies
- [ ] Add request debouncing for search functionality
- [ ] Optimize image upload and processing

---

## Phase 10: Documentation and Maintenance 🟢

### 10.1 Code Documentation
- [ ] Add comprehensive documentation to all public APIs
- [ ] Document error handling patterns
- [ ] Create usage examples for complex operations
- [ ] Update architecture documentation

### 10.2 User Documentation
- [ ] Update user guides for new social features
- [ ] Document privacy and blocking features
- [ ] Create troubleshooting guides

### 10.3 Monitoring and Analytics
- [ ] Add analytics for social feature usage
- [ ] Implement error tracking and monitoring
- [ ] Add performance monitoring for API calls

---

## Implementation Notes

### Authentication
- All endpoints require Bearer token authentication
- Token should be included in Authorization header
- Handle 401 responses by redirecting to login

### Error Handling
- API uses consistent error response format with `ErrorDetail`
- Common HTTP status codes: 400, 401, 404, 409, 500
- Implement proper user-friendly error messages

### Pagination
- API uses cursor-based pagination with `cursor` and `limit` parameters
- Default limit is 20, maximum is 100
- Response includes `has_more` and `next_cursor` fields

### Data Consistency
- Implement optimistic updates for better UX
- Handle race conditions in follow/unfollow operations
- Ensure UI reflects server state accurately

### Performance Considerations
- Implement request caching where appropriate
- Use batch operations for multiple user operations
- Optimize image uploads with compression
- Implement proper loading states and skeleton screens

---

## Estimated Timeline
- **Phase 1-2**: 2-3 weeks (Core infrastructure and user management)
- **Phase 3**: 1-2 weeks (Follow system)
- **Phase 4**: 2-3 weeks (Friendship system)
- **Phase 5**: 1 week (Blocking system)
- **Phase 6**: 1 week (Advanced relationships)
- **Phase 7**: 2-3 weeks (State management)
- **Phase 8**: 2-3 weeks (UI integration)
- **Phase 9**: 2-3 weeks (Testing)
- **Phase 10**: 1 week (Documentation)

**Total Estimated Time**: 12-18 weeks

This checklist provides a comprehensive roadmap for integrating all User Service API endpoints while maintaining code quality, following existing patterns, and ensuring a great user experience.
