import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_social/cubit/auth/profile/profile_cubit.dart';
import 'package:toii_social/cubit/post/comment/comment_cubit.dart';
import 'package:toii_social/cubit/post/create_comment/create_comment_cubit.dart';
import 'package:toii_social/cubit/post/delete_update_post/delete_update_cubit.dart';
import 'package:toii_social/cubit/post/delete_update_post/delete_update_state.dart';
import 'package:toii_social/cubit/post/detail_post/detail_post_cubit.dart';
import 'package:toii_social/cubit/post/hide_post/hide_post_cubit.dart';
import 'package:toii_social/cubit/post/hide_post/hide_post_state.dart';
import 'package:toii_social/cubit/post/home/<USER>';
import 'package:toii_social/model/comment/comment_model.dart';
import 'package:toii_social/model/post/post_model.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/screen/home/<USER>/widget/comment_inputbar_widget.dart';
import 'package:toii_social/screen/home/<USER>/widget/comment_widget.dart';
import 'package:toii_social/screen/home/<USER>/bottom_sheet_more_action.dart';
import 'package:toii_social/screen/home/<USER>/home_item_post_widget.dart';
import 'package:toii_social/screen/home/<USER>/post/dialog_confirm_delete_post.dart';
import 'package:toii_social/widget/app_bar/app_bar.dart';
import 'package:toii_social/widget/bottom_sheet/tt_bottom_sheet.dart';
import 'package:toii_social/widget/scaffords/scafford.dart';
import 'package:toii_social/widget/snack_bar/snackbar.dart';

class HomeDetailsArguments {
  final PostModel postModel;
  final bool? isForcusComment;
  const HomeDetailsArguments({
    required this.postModel,
    this.isForcusComment = false,
  });
}

class HomeDetailsScreen extends StatefulWidget {
  final HomeDetailsArguments arg;
  const HomeDetailsScreen({super.key, required this.arg});

  @override
  State<HomeDetailsScreen> createState() => _HomeDetailsScreenState();
}

class _HomeDetailsScreenState extends State<HomeDetailsScreen> {
  late CommentCubit _commentCubit;
  late DetailPostCubit _detailPostCubit;
  late HidePostCubit _hidePostCubit;
  late DeleteUpdatePostCubit _deleteUpdatePostCubit;
  final TextEditingController _commentController = TextEditingController();
  final FocusNode _commentFocusNode = FocusNode();

  @override
  void initState() {
    _commentCubit = CommentCubit(postId: widget.arg.postModel.id);
    _detailPostCubit = DetailPostCubit();
    _hidePostCubit = HidePostCubit();
    _deleteUpdatePostCubit = DeleteUpdatePostCubit();
    _detailPostCubit.fetchPostDetail(widget.arg.postModel.id);

    if (widget.arg.isForcusComment == true) {
      // Sử dụng addPostFrameCallback để đảm bảo widget đã được render trước khi focus
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _commentFocusNode.requestFocus();
      });
    }

    super.initState();
  }

  Widget _bodyWidget() {
    return BlocBuilder<DetailPostCubit, DetailPostState>(
      bloc: _detailPostCubit,
      builder: (context, detailState) {
        if (detailState.status == DetailPostStatus.loading) {
          return const Center(child: CircularProgressIndicator());
        } else if (detailState.status == DetailPostStatus.failure) {
          return Center(child: Text(detailState.errorMessage ?? ''));
        } else if (detailState.status == DetailPostStatus.success &&
            detailState.post != null) {
          return SafeArea(
            child: SingleChildScrollView(
              keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
              child: Column(
                children: [
                  HomeItemPostWidget(
                    post: detailState.post!,
                    isNotOnTap: true,
                    isShowActionMore: false,
                  ),
                  BlocBuilder<CommentCubit, CommentState>(
                    builder: (context, state) {
                      if (state.status.isLoading) {
                        return const Center(child: CircularProgressIndicator());
                      } else if (state.status.isSuccess) {
                        return _commentListWidget(comments: state.comments);
                      } else if (state.status.isFailure) {
                        return Center(child: Text(state.errorMessage ?? ''));
                      }
                      return const SizedBox.shrink();
                    },
                  ),
                ],
              ),
            ),
          );
        }
        return const SizedBox.shrink();
      },
    );
  }

  Widget _commentListWidget({required List<CommentItemModel> comments}) {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16).copyWith(top: 0),
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: comments.length,
      itemBuilder: (context, index) {
        return CommentWidget(commentItemModel: comments[index]);
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (_) => _commentCubit..getComments()),
        BlocProvider(
          create: (_) => CreateCommentCubit(postId: widget.arg.postModel.id),
        ),
        BlocProvider(create: (_) => _detailPostCubit),
        BlocProvider(create: (_) => _hidePostCubit),

        BlocProvider(create: (_) => _deleteUpdatePostCubit),
      ],
      child: BaseScaffold(
        resizeToAvoidBottomInset: true,
        title: Text('Post'),
        appbar: BaseAppBar(
          title: ('Post'),
          actions: [
            IconButton(
              onPressed: () {
                showTtBottomSheet(
                  context,
                  child: BottomSheetMoreAction(
                    isPostByUser:
                        widget.arg.postModel.user?.id ==
                        GetIt.instance<ProfileCubit>().state.userModel?.id,
                    onEdit: () {
                      context
                          .push(
                            RouterEnums.createPost.routeName,
                            extra: widget.arg.postModel,
                          )
                          .then((value) {
                            _detailPostCubit.fetchPostDetail(
                              widget.arg.postModel.id,
                            );
                          });
                    },
                    onHide: () {
                      _hidePostCubit.hidePost(widget.arg.postModel.id);
                    },
                    onDelete: () {
                      showDialogConfirmDeletePost(
                        context: context,
                        onCancel: () {
                          context.pop();
                        },
                        onDelete: () {
                          context.pop();
                          _deleteUpdatePostCubit.deletePost(
                            widget.arg.postModel.id,
                          );
                        },
                      );
                    },
                  ),
                  isShowClose: true,
                  isDismissible: true,
                );
              },
              icon: const Icon(Icons.more_horiz),
            ),
          ],
        ),
        body: _bodyWidget(),
        bottom: MultiBlocListener(
          listeners: [
            BlocListener<HidePostCubit, HidePostState>(
              listener: (context, state) {
                if (state.status == HidePostStatus.success) {
                  context.showSnackbar(message: "Post hidden successfully");
                  context.pop();
                  GetIt.instance<HomeCubit>().getUserFeed(isRefresh: true);
                }
                if (state.status == HidePostStatus.failure) {
                  context.showSnackbar(message: state.errorMessage ?? "");
                }
              },
            ),
            BlocListener<DeleteUpdatePostCubit, DeleteUpdatePostState>(
              listener: (context, state) {
                if (state.status == DeleteUpdatePostStatus.success) {
                  context.showSnackbar(message: "Post deleted successfully");
                  context.pop();
                  GetIt.instance<HomeCubit>().getUserFeed(isRefresh: true);
                }
                if (state.status == DeleteUpdatePostStatus.failure) {
                  context.showSnackbar(message: state.errorMessage ?? "");
                }
              },
            ),
          ],
          child: BlocConsumer<CreateCommentCubit, CreateCommentState>(
            listener: (context, state) async {
              if (state.status.isSuccess) {
                context.showSnackbar(message: "Comment successfully");
                _commentCubit.addComment(state.comment!);
              }
            },
            builder: (context, state) {
              return Padding(
                padding: MediaQuery.of(context).viewInsets,
                child: CommentInputBarWidget(
                  controller: _commentController,
                  focusNode: _commentFocusNode,
                  onSend: (value) {
                    context.read<CreateCommentCubit>().createAComment(
                      content: value,
                    );
                  },
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
