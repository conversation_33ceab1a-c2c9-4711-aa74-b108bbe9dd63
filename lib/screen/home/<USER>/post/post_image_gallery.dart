import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_social/model/post/post_model.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/screen/home/<USER>/detail_preview_img.dart';
import 'package:toii_social/screen/home/<USER>/multi_image/src/newsfeed_multiple_imageview.dart';
import 'package:toii_social/widget/images/cached_network_image_widget.dart';

class PostImageGallery extends StatelessWidget {
  final PostModel post;
  const PostImageGallery({super.key, required this.post});

  @override
  Widget build(BuildContext context) {
    final images = post.mediaDetails;
    if (images.isEmpty) {
      return const SizedBox(height: 16);
    }

    return NewsfeedMultipleImageView(
      imageUrls: images,
      marginLeft: 10.0,
      marginRight: 10.0,
      marginBottom: 10.0,
      marginTop: 10.0,
      post: post,
    );
  }
}
