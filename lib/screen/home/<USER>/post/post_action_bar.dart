import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/model/post/post_model.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/screen/home/<USER>/home_details_screen.dart';
import 'package:toii_social/screen/home/<USER>/post/post_icon_text.dart';
import 'package:toii_social/screen/home/<USER>/post/post_like_button.dart';
import 'package:toii_social/screen/home/<USER>/post/post_repost_button.dart';
import 'package:toii_social/screen/home/<USER>/post/post_share_button.dart';
import 'package:toii_social/widget/colors/colors.dart';

class PostActionBar extends StatelessWidget {
  final PostModel post;
  const PostActionBar({super.key, required this.post});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SizedBox(
        width: 256,
        height: 40,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 4, sigmaY: 4),
            child: Container(
              height: 40,
              color: themeData.black800,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Expanded(
                    child: Center(
                      child: PostLikeButton(
                        post: post,
                        initialLiked: post.isLiked,
                      ),
                    ),
                  ),

                  Expanded(
                    child: Center(
                      child: PostIconText(
                        icon: Assets.icons.icResponses.svg(
                          width: 20,
                          height: 20,
                        ),
                        text: post.getViewComment,
                        onTap: () {
                          context.push(
                            RouterEnums.homeDetails.routeName,
                            extra: HomeDetailsArguments(
                              postModel: post,
                              isForcusComment: true,
                            ),
                          );
                        },
                      ),
                    ),
                  ),

                  Expanded(child: Center(child: PostRepostButton(post: post))),

                  Expanded(child: Center(child: PostShareButton(post: post))),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
