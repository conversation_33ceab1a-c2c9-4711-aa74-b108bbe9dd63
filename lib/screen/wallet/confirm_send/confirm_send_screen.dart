import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_social/cubit/auth/profile/profile_cubit.dart';
import 'package:toii_social/cubit/send_token/send_token_cubit.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/cubit/wallet/wallet_cubit.dart';
import 'package:toii_social/model/chain/token_item_model.dart';
import 'package:toii_social/model/crypto/asset.dart';
import 'package:toii_social/screen/wallet/widgets/crypto_logo_network.dart';
import 'package:toii_social/utils/bio/authentication_service.dart';
import 'package:toii_social/utils/keychain/keychain_service.dart';
import 'package:toii_social/utils/navigation_service.dart';
import 'package:toii_social/widget/button/button.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/scaffords/scafford.dart';
import 'package:toii_social/widget/snack_bar/snackbar.dart';

import 'widget/loading.dart';
import 'widget/transaction_info.dart';

class ConfirmSendScreenArgs {
  final Asset token;
  final String address;
  final String amount;
  final String recipientName;
  final String networkFee;
  ConfirmSendScreenArgs({
    required this.token,
    required this.address,
    required this.amount,
    this.recipientName = 'User',
    required this.networkFee,
  });
}

class ConfirmSendScreen extends StatefulWidget {
  final ConfirmSendScreenArgs arg;

  const ConfirmSendScreen({super.key, required this.arg});

  @override
  State<ConfirmSendScreen> createState() => _ConfirmSendScreenState();
}

class _ConfirmSendScreenState extends State<ConfirmSendScreen> {
  String get _tokenSymbol => widget.arg.token.crypto.symbol ?? 'TOII';

  double get amount {
    final text = widget.arg.amount.replaceAll(",", ".");
    if (text.isEmpty) return 0;
    return double.tryParse(text) ?? 0;
  }

  String get _amountUsd {
    final usd = amount * (widget.arg.token.cryptoPrice ?? 0);
    return '\$${usd.toStringAsFixed(2)}';
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => SendTokenCubit(),
      child: Stack(
        children: [
          BaseScaffold(
            showLeading: true,
            title: Text(
              'Confirm send',
              style: titleLarge.copyColor(themeData.neutral800),
            ),
            body: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 16),

                        // Amount Section
                        _buildAmountSection(),

                        const SizedBox(height: 16),

                        // Recipient Address Section
                        _buildRecipientSection(),

                        const SizedBox(height: 16),

                        // Network Section
                        _buildNetworkSection(),

                        const SizedBox(height: 16),

                        // Network Fee Section
                        _buildNetworkFeeSection(),

                        const SizedBox(height: 32),

                        // Wallet Section
                        _buildWalletSection(),

                        const SizedBox(height: 40),
                      ],
                    ),
                  ),
                ),

                // Action Buttons
                _buildActionButtons(),
              ],
            ),
          ),
          BlocConsumer<SendTokenCubit, SendTokenState>(
            listener: (context, state) {
              if (state.errors != null) {}
            },
            builder: (context, state) {
              return state.loading
                  ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Loading(),
                        if (state.transactionId != null)
                          TransactionInfo(transactionId: state.transactionId!),
                        // explorerUrl: network.config.explorerUrl)
                      ],
                    ),
                  )
                  : const SizedBox();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAmountSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Amount', style: titleMedium.copyColor(themeData.neutral400)),

        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: themeData.neutral100,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              CryptoLogoNetwork(
                id: widget.arg.token.idLogo.toString(),
                name: widget.arg.token.crypto.symbol ?? '',
                size: 40,
              ),

              // Token Icon
              const SizedBox(width: 12),

              // Token Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _tokenSymbol,
                      style: titleMedium.copyColor(themeData.neutral800),
                    ),
                    Text(
                      _tokenSymbol,
                      style: titleMedium.copyColor(themeData.neutral400),
                    ),
                  ],
                ),
              ),

              // Amount
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '- $amount $_tokenSymbol',
                    style: titleMedium.copyColor(themeData.neutral800),
                  ),
                  Text(
                    _amountUsd,
                    style: bodyMedium.copyColor(themeData.neutral400),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildRecipientSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recipient address',
          style: titleMedium.copyColor(themeData.neutral400),
        ),

        const SizedBox(height: 6),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 12),
          decoration: BoxDecoration(
            border: Border.all(color: themeData.neutral200),
            color: themeData.neutral50,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.arg.recipientName,
                style: titleMedium.copyColor(themeData.neutral800),
              ),

              Text(
                widget.arg.address,
                style: labelLarge.copyColor(themeData.neutral800),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildNetworkSection() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text('Network', style: titleMedium.copyColor(themeData.neutral400)),

        Row(
          children: [
            CryptoLogoNetwork(
              id: widget.arg.token.idLogo.toString(),
              name: widget.arg.token.crypto.symbol ?? '',
              size: 24,
            ),
            const SizedBox(width: 8),
            Text(
              _tokenSymbol,
              style: titleMedium.copyColor(themeData.neutral800),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildNetworkFeeSection() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text('Network fee', style: titleMedium.copyColor(themeData.neutral400)),

        Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              '${widget.arg.networkFee} $_tokenSymbol',
              style: titleMedium.copyColor(themeData.neutral800),
            ),
            // Text(
            //   '\$${(double.tryParse(widget.arg.networkFee) ?? 0) * (widget.arg.token.price ?? 0)}',
            //   style: titleSmall.copyColor(themeData.neutral400),
            // ),
          ],
        ),
      ],
    );
  }

  Widget _buildWalletSection() {
    final address =
        GetIt.instance<ProfileCubit>().state.userModel?.walletAddress ?? "";
    final userName =
        GetIt.instance<ProfileCubit>().state.userModel?.username ?? "User";

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          height: 48,
          child: Text(
            'Wallet',
            style: titleMedium.copyColor(themeData.neutral400),
          ),
        ),

        const SizedBox(height: 6),

        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            border: Border.all(color: themeData.neutral200),
            color: themeData.neutral50,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              // Avatar
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: themeData.neutral300,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.person,
                  color: themeData.neutral800,
                  size: 20,
                ),
              ),

              const SizedBox(width: 12),

              // Wallet Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      userName,
                      style: bodyMedium.copyColor(themeData.neutral800),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      address,
                      style: bodySmall.copyColor(themeData.neutral400),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return BlocBuilder<SendTokenCubit, SendTokenState>(
      builder: (context, state) {
        return Container(
          margin: const EdgeInsets.symmetric(
            horizontal: 16,
          ).copyWith(bottom: MediaQuery.of(context).padding.bottom + 16),
          width: double.infinity,
          child: TSButton.primary(
            title: 'Confirm',
            onPressed: () {
              _confirmTransaction(context);
            },
          ),
        );
      },
    );
  }

  void _rejectTransaction() {
    Navigator.of(context).popUntil((route) => route.isFirst);
  }

  void _confirmTransaction(BuildContext context) {
    final topContext = GetIt.instance<NavigationService>().navigatorContext;

    final authenticationService = AuthenticationService(
      onLoginSuccess: () async {
        final privateKey = await KeychainService.instance
            .readPrivateKeyFromiCloud(
              GetIt.instance<ProfileCubit>().state.userModel?.walletAddress ??
                  "",
            );
        final transId = context.read<SendTokenCubit>().transfer(
          token: widget.arg.token.crypto,
          contractAddressHex: null,
          privateKey: privateKey ?? "",
          destinationAddress: widget.arg.address,
          quantity: amount,
        );
        if (transId != null) {
          GetIt.instance<WalletCubit>().getToken(
            walletAddress:
                GetIt.instance<ProfileCubit>().state.userModel?.walletAddress ??
                "",
          );
          if (mounted) {
            // ignore: use_build_context_synchronously

            Navigator.of(
              context,
            ).popUntil((route) => route.isFirst); // Go back to wallet
            Future.delayed(const Duration(seconds: 1), () {
              getContext.showSnackbar(
                message: 'Transaction sent successfully!',
              );
            });
          }
        }
      },
    );
    authenticationService.authCheck(topContext);
  }
}
