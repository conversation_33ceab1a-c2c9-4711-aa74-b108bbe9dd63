import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import 'package:toii_social/cubit/auth/profile/profile_cubit.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/cubit/wallet/wallet_cubit.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/model/crypto/asset.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/screen/chat/lib/utils/extensions.dart';
import 'package:toii_social/screen/wallet/widgets/crypto_logo_network.dart';
import 'package:toii_social/utils/utils.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/pull_refresh/app_refresh_view.dart';
import 'package:toii_social/widget/scaffords/scafford.dart';

class WalletScreen extends StatelessWidget {
  const WalletScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create:
          (context) =>
              GetIt.instance<WalletCubit>()..getToken(
                walletAddress:
                    GetIt.instance<ProfileCubit>()
                        .state
                        .userModel
                        ?.walletAddress ??
                    "",
              ),
      child: BlocListener<ProfileCubit, ProfileState>(
        listenWhen:
            (previous, current) =>
                previous.userModel?.walletAddress !=
                    current.userModel?.walletAddress &&
                current.userModel?.walletAddress != null,
        listener: (context, state) {
          //  if (state.status.isSuccess && state.userModel != null) {
          context.read<WalletCubit>().getToken(
            walletAddress: state.userModel!.walletAddress ?? "",
          );
          //  }
        },
        child: BlocBuilder<ProfileCubit, ProfileState>(
          builder: (context, state) {
            return const _WalletScreenContent();
          },
        ),
      ),
    );
  }
}

class _WalletScreenContent extends StatefulWidget {
  const _WalletScreenContent();

  @override
  State<_WalletScreenContent> createState() => _WalletScreenContentState();
}

class _WalletScreenContentState extends State<_WalletScreenContent>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _showNFTDetection = true;
  int _selectedTabIndex = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BaseScaffold(
      //    backgroundColor: theme.neutral50,
      title: Row(
        children: [
          Text(
            'My Wallet',
            style: headlineMedium.copyWith(
              color: themeData.neutral800,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
      body: BlocBuilder<WalletCubit, WalletState>(
        builder: (context, state) {
          return PullToRefreshView(
            onRefresh: () async {
              return context.read<WalletCubit>().getToken(
                walletAddress:
                    context
                        .read<ProfileCubit>()
                        .state
                        .userModel
                        ?.walletAddress ??
                    "",
              );
            },
            child: SingleChildScrollView(
              child: Column(
                children: [
                  // Wallet Address Header
                  //      _buildWalletAddressHeader(),
                  const SizedBox(height: 8),
                  // Balance Card
                  _buildBalanceCard(context),

                  const SizedBox(height: 24),

                  // Action Buttons
                  _buildActionButtons(context),

                  const SizedBox(height: 16),
                  Container(height: 2, color: themeData.neutral200),
                  const SizedBox(height: 16),
                  _buildTokensNFTsSection(context),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildWalletAddressHeader() {
    return BlocBuilder<ProfileCubit, ProfileState>(
      builder: (context, state) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                state.userModel?.username ?? "",
                style: titleLarge.copyColor(themeData.neutral800),
              ),
              Row(
                children: [
                  Text(
                    shortenAddress(state.userModel?.walletAddress ?? ''),
                    style: bodyMedium.copyWith(
                      color: themeData.neutral800,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(width: 8),
                  GestureDetector(
                    onTap:
                        () => _copyToClipboard(
                          context,
                          state.userModel?.walletAddress ?? '',
                        ),
                    child: Icon(
                      Icons.copy,
                      size: 16,
                      color: themeData.neutral800,
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildBalanceCard(BuildContext context) {
    return BlocBuilder<WalletCubit, WalletState>(
      builder: (context, state) {
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          child: Stack(
            children: [
              Assets.images.bgWallet1.image(),
              Container(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [_buildWalletAddressHeader()],
                        ),

                        const Spacer(),

                        GestureDetector(
                          onTap: () {
                            context.push(RouterEnums.scanQr.routeName);
                          },
                          child: SvgPicture.asset(Assets.icons.icScanQr.path),
                        ),
                      ],
                    ),

                    const SizedBox(height: 24),

                    // Balance amount
                    Text(
                      '\$${state.totalBalance.toStringAsFixed(2)}',
                      style: headlineLarge.copyColor(themeData.black900),
                    ),

                    const SizedBox(height: 4),

                    Text(
                      'Balance',
                      style: labelLarge.copyColor(themeData.black600),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildActionButton(
            path: Assets.icons.icSend.path,
            label: 'Send',
            onTap: () => _showSendDialog(context),
          ),
          _buildActionButton(
            path: Assets.icons.icReceive.path,
            label: 'Receive',
            onTap: () => _showReceiveDialog(context),
          ),
          _buildActionButton(
            path: Assets.icons.icEarn.path,
            label: 'Earn',
            onTap: () => _showHistoryScreen(context),
          ),
          _buildActionButton(
            path: Assets.icons.icDapp.path,
            label: 'DApps',
            onTap: () => _showDAppsScreen(context),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required String path,
    required String label,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);

    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 56,
            height: 56,
            decoration: BoxDecoration(
              color: theme.neutral100,
              borderRadius: BorderRadius.circular(100),
            ),
            child: Center(child: SvgPicture.asset(path)),
          ),
          const SizedBox(height: 8),
          Text(label, style: bodyMedium.copyColor(themeData.neutral800)),
        ],
      ),
    );
  }

  Widget _buildTokensNFTsSection(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          // Custom Tab bar
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            child: Row(
              children: [
                _buildCustomTabButton('Tokens', 0),
                const SizedBox(width: 32),
                _buildCustomTabButton('NFTs', 1),
              ],
            ),
          ),

          // Tab content
          _selectedTabIndex == 0
              ? _buildTokensTab(context)
              : _buildNFTsTab(context),
        ],
      ),
    );
  }

  Widget _buildCustomTabButton(String title, int index) {
    final theme = Theme.of(context);
    final isSelected = _selectedTabIndex == index;

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedTabIndex = index;
        });
      },
      child: Container(
        padding: const EdgeInsets.only(bottom: 8),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: isSelected ? theme.primaryGreen500 : Colors.transparent,
              width: 2,
            ),
          ),
        ),
        child: Text(
          title,
          style: bodyMedium.copyWith(
            color: isSelected ? theme.textPrimary : theme.textSecondary,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget _buildTokensTab(BuildContext context) {
    return BlocBuilder<WalletCubit, WalletState>(
      builder: (context, state) {
        final theme = Theme.of(context);
        return Column(
          children: [
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              child: OutlinedButton.icon(
                onPressed: () => _showAddTokensDialog(context),
                icon: Icon(LucideIcons.bolt, size: 20),
                label: const Text('Manage tokens'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: theme.textSecondary,
                  side: BorderSide(color: theme.neutral300),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
            // Divider
            Container(
              height: 1,
              color: theme.neutral200,
              margin: const EdgeInsets.symmetric(horizontal: 16),
            ),

            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemBuilder:
                  (context, index) => GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onTap: () {
                      context.push(
                        RouterEnums.tokenDetails.routeName,
                        extra: state.tokens[index],
                      );
                    },
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        children: [
                          // Token icon
                          CryptoLogoNetwork(
                            name: state.tokens[index].crypto.symbol,
                            id: state.tokens[index].idLogo.toString(),
                          ),

                          const SizedBox(width: 12),
                          // Token info
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Text(
                                      "${state.tokens[index].crypto.symbol?.toUpperCase() ?? ""} (${state.tokens[index].crypto.shortName?.capitalize() ?? state.tokens[index].crypto.name})",
                                      style: bodyMedium.copyWith(
                                        color: theme.textPrimary,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 2),
                                Text(
                                  state.tokens[index].cryptoPrice == null
                                      ? "\$-"
                                      : "\$${state.tokens[index].cryptoPrice?.toStringAsFixed(2)}",

                                  style: bodyMedium.copyColor(
                                    themeData.neutral400,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          // Token balance
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              Text(
                                '${(state.tokens[index].balanceCrypto)?.toStringAsFixed(4)} ${state.tokens[index].crypto.symbol}',
                                style: bodyMedium.copyWith(
                                  color: theme.textPrimary,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              const SizedBox(height: 2),
                              Text(
                                '\$${((state.tokens[index].balanceUsd ?? 0) * (state.tokens[index].cryptoPrice ?? 0)).toStringAsFixed(2)}',
                                style: bodySmall.copyWith(
                                  color: theme.textSecondary,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
              separatorBuilder: (context, index) => const SizedBox(height: 8),
              itemCount: state.tokens.length,
            ),

            // Add tokens button
            const SizedBox(height: 16),
          ],
        );
      },
    );
  }

  Widget _buildNFTsTab(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      children: [
        // NFT detection banner
        if (_showNFTDetection)
          Container(
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.neutral50,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'NFT detection',
                        style: bodyMedium.copyWith(
                          color: theme.textPrimary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Allow Toii to automatically detect and display NFTs in your wallet.',
                        style: bodySmall.copyWith(color: theme.textSecondary),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () {
                    setState(() {
                      _showNFTDetection = false;
                    });
                  },
                  icon: Icon(Icons.close, color: theme.textSecondary, size: 20),
                ),
              ],
            ),
          ),

        // Import NFT button
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () => _showImportNFTDialog(context),
              icon: const Icon(Icons.add, size: 20),
              label: const Text('Import NFT'),
              style: OutlinedButton.styleFrom(
                foregroundColor: theme.textSecondary,
                side: BorderSide(color: theme.neutral300),
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ),

        const SizedBox(height: 32),
      ],
    );
  }

  // Dialog and utility methods
  void _copyToClipboard(BuildContext context, String address) {
    Clipboard.setData(ClipboardData(text: address));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Address copied to clipboard')),
    );
  }

  void _showSendDialog(BuildContext context) async {
    final result = await context.push(
      RouterEnums.selectedToken.routeName,
      extra: context.read<WalletCubit>().state.tokens.toList(),
    );
    if (result is Asset) {
      context.push(
        RouterEnums.sendToken.routeName,
        extra: result.crypto,
        // extra: context.read<ProfileCubit>().state.userModel?.walletAddress,
      );
    }
    // Implement send dialog
  }

  void _showReceiveDialog(BuildContext context) async {
    final result = await context.push(
      RouterEnums.selectedToken.routeName,
      extra: context.read<WalletCubit>().state.tokens.toList(),
    );
    if (result is Asset) {
      context.push(
        RouterEnums.receiveToken.routeName,
        extra: result.crypto,
        // extra: context.read<ProfileCubit>().state.userModel?.walletAddress,
      );
    }
  }

  void _showHistoryScreen(BuildContext context) {
    // Navigate to history screen
  }

  void _showDAppsScreen(BuildContext context) {
    // Navigate to DApps screen
  }

  void _showAddTokensDialog(BuildContext context) {
    context.push(RouterEnums.importToken.routeName);
    // showBottomSheetAddCustomToken(
    //   context,
    //   context.read<WalletCubit>().state.currentActiveChain,
    // );
  }

  void _showImportNFTDialog(BuildContext context) {
    // Implement import NFT dialog
  }
}
