import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/widget/button/button.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/scaffords/scafford.dart';
import 'package:toii_social/widget/snack_bar/snackbar.dart';

class ImportTokenScreen extends StatefulHookConsumerWidget {
  const ImportTokenScreen({super.key});

  @override
  ConsumerState<ImportTokenScreen> createState() => _ImportTokenScreenState();
}

class _ImportTokenScreenState extends ConsumerState<ImportTokenScreen> {
  final TextEditingController _contractAddressController =
      TextEditingController();
  final TextEditingController _tokenSymbolController = TextEditingController();
  final TextEditingController _tokenPrecisionController =
      TextEditingController();

  String _selectedNetwork = 'Select Network';
  bool _isTokenDetected = false;

  final List<String> _networks = [
    'Select Network',
    'BNB Smart Chain',
    'Ethereum',
    'Polygon',
    'Arbitrum',
    'Optimism',
  ];

  bool get _canProceed {
    return _selectedNetwork != 'Select Network' &&
        _contractAddressController.text.isNotEmpty &&
        _tokenSymbolController.text.isNotEmpty &&
        _tokenPrecisionController.text.isNotEmpty;
  }

  @override
  void initState() {
    super.initState();
    _contractAddressController.addListener(_onContractAddressChanged);
  }

  @override
  void dispose() {
    _contractAddressController.dispose();
    _tokenSymbolController.dispose();
    _tokenPrecisionController.dispose();
    super.dispose();
  }

  void _onContractAddressChanged() {
    if (_contractAddressController.text.length >= 20) {
      _detectToken();
    } else {
      setState(() {
        _isTokenDetected = false;
        _tokenSymbolController.clear();
        _tokenPrecisionController.clear();
      });
    }
  }

  void _detectToken() {
    setState(() {
      _isTokenDetected = true;
      _tokenSymbolController.text = 'CAKE';
      _tokenPrecisionController.text = '18';
    });
  }

  @override
  Widget build(BuildContext context) {
    return BaseScaffold(
      showLeading: true,
      title: Text(
        'Import Tokens',
        style: titleLarge.copyColor(themeData.neutral800),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Warning Banner
                    _buildWarningBanner(),

                    const SizedBox(height: 24),

                    // Network Section
                    _buildNetworkSection(),

                    const SizedBox(height: 24),

                    // Contract Address Section
                    _buildContractAddressSection(),

                    const SizedBox(height: 24),

                    // Token Symbol Section
                    _buildTokenSymbolSection(),

                    const SizedBox(height: 24),

                    // Token Precision Section
                    _buildTokenPrecisionSection(),
                  ],
                ),
              ),
            ),

            // Next Button
            _buildNextButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildWarningBanner() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFFFFF8E1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFFFFE082)),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 20,
            height: 20,
            decoration: const BoxDecoration(
              color: Color(0xFFFF8F00),
              shape: BoxShape.circle,
            ),
            child: const Icon(Icons.warning, color: Colors.white, size: 12),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'Anyone can create a token, including creating fake versions of existing tokens',
              style: bodySmall.copyColor(const Color(0xFFE65100)),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNetworkSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Network', style: bodyMedium.copyColor(themeData.neutral800)),
        const SizedBox(height: 8),
        GestureDetector(
          onTap: _showNetworkSelector,
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: themeData.neutral100,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: themeData.neutral200),
            ),
            child: Row(
              children: [
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color:
                        _selectedNetwork == 'BNB Smart Chain'
                            ? const Color(0xFFF0B90B)
                            : themeData.neutral400,
                    shape: BoxShape.circle,
                  ),
                  child:
                      _selectedNetwork == 'BNB Smart Chain'
                          ? const Text(
                            'B',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          )
                          : Icon(Icons.language, color: Colors.white, size: 12),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    _selectedNetwork,
                    style: bodyMedium.copyColor(
                      _selectedNetwork == 'Select Network'
                          ? themeData.neutral400
                          : themeData.neutral800,
                    ),
                  ),
                ),
                Icon(
                  Icons.keyboard_arrow_down,
                  color: themeData.neutral400,
                  size: 20,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildContractAddressSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Contract Address',
          style: bodyMedium.copyColor(themeData.neutral800),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: themeData.neutral100,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: themeData.neutral200),
          ),
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _contractAddressController,
                  style: bodyMedium.copyColor(themeData.neutral800),
                  decoration: InputDecoration(
                    hintText: 'Enter Address',
                    hintStyle: bodyMedium.copyColor(themeData.neutral400),
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.all(16),
                  ),
                ),
              ),
              GestureDetector(
                onTap: _showQRScanner,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Icon(
                    Icons.qr_code_scanner,
                    color: themeData.neutral400,
                    size: 20,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTokenSymbolSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Token Symbol', style: bodyMedium.copyColor(themeData.neutral800)),
        const SizedBox(height: 8),
        TextField(
          controller: _tokenSymbolController,
          readOnly: _isTokenDetected,
          style: bodyMedium.copyColor(
            _isTokenDetected ? themeData.neutral400 : themeData.neutral800,
          ),
          decoration: InputDecoration(
            filled: true,
            fillColor: themeData.neutral100,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: themeData.neutral200),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: themeData.neutral200),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: themeData.primaryGreen500),
            ),
            contentPadding: const EdgeInsets.all(16),
          ),
        ),
      ],
    );
  }

  Widget _buildTokenPrecisionSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Token of precision',
          style: bodyMedium.copyColor(themeData.neutral800),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: _tokenPrecisionController,
          readOnly: _isTokenDetected,
          keyboardType: TextInputType.number,
          style: bodyMedium.copyColor(
            _isTokenDetected ? themeData.neutral400 : themeData.neutral800,
          ),
          decoration: InputDecoration(
            filled: true,
            fillColor: themeData.neutral100,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: themeData.neutral200),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: themeData.neutral200),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: themeData.primaryGreen500),
            ),
            contentPadding: const EdgeInsets.all(16),
          ),
        ),
      ],
    );
  }

  Widget _buildNextButton() {
    return Column(
      children: [
        const SizedBox(height: 24),
        SizedBox(
          width: double.infinity,
          child: TSButton.primary(
            title: 'Next',
            onPressed: () {},
            //    onPressed: _canProceed ? _proceedToNext : null,
          ),
        ),
        const SizedBox(height: 16),
        // Bottom indicator
        Container(
          width: 134,
          height: 5,
          decoration: BoxDecoration(
            color: themeData.neutral800,
            borderRadius: BorderRadius.circular(2.5),
          ),
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  void _showNetworkSelector() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: themeData.neutral300,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(height: 20),
                Text(
                  'Select Network',
                  style: titleLarge.copyColor(themeData.neutral800),
                ),
                const SizedBox(height: 20),
                ...(_networks
                    .skip(1)
                    .map(
                      (network) => ListTile(
                        title: Text(
                          network,
                          style: bodyMedium.copyColor(themeData.neutral800),
                        ),
                        leading: Container(
                          width: 24,
                          height: 24,
                          decoration: BoxDecoration(
                            color:
                                network == 'BNB Smart Chain'
                                    ? const Color(0xFFF0B90B)
                                    : themeData.neutral400,
                            shape: BoxShape.circle,
                          ),
                          child:
                              network == 'BNB Smart Chain'
                                  ? const Text(
                                    'B',
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 12,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  )
                                  : Icon(
                                    Icons.language,
                                    color: Colors.white,
                                    size: 12,
                                  ),
                        ),
                        trailing:
                            _selectedNetwork == network
                                ? Icon(
                                  Icons.check,
                                  color: themeData.primaryGreen500,
                                )
                                : null,
                        onTap: () {
                          setState(() {
                            _selectedNetwork = network;
                          });
                          Navigator.pop(context);
                        },
                      ),
                    )),
                const SizedBox(height: 20),
              ],
            ),
          ),
    );
  }

  void _showQRScanner() {
    context.showSnackbar(message: 'QR Scanner feature coming soon...');
  }

  void _proceedToNext() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Token Import'),
            content: Text(
              'Import ${_tokenSymbolController.text} token from ${_selectedNetwork}?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  Navigator.pop(context);
                  context.showSnackbar(
                    message:
                        '${_tokenSymbolController.text} token imported successfully!',
                  );
                },
                child: const Text('Import'),
              ),
            ],
          ),
    );
  }
}
