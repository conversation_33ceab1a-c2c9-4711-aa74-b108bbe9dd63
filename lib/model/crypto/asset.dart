import 'package:toii_social/model/crypto/crypto.dart';

class Asset {
  final Crypto crypto;
  final double? balanceUsd;
  final double? balanceCrypto;
  final String? cryptoTrendPercent;
  final double? cryptoPrice;
  final int idLogo;

  Asset({
    required this.crypto,
     this.balanceUsd = 0.0,
     this.balanceCrypto = 0.0,
     this.cryptoTrendPercent,
     this.cryptoPrice,
    this.idLogo = -1
  });

  // Convert a JSON Map to a HistoryItem instance
  factory Asset.fromJson(Map<String, dynamic> json) {
    return Asset(
      crypto: Crypto.fromJson(json['crypto']),
      balanceUsd: json['balanceUsd'] ?? "0",
      balanceCrypto: json['balanceCrypto'] ?? "0",
      cryptoTrendPercent: json['cryptoTrendPercent'] ?? "0",
      cryptoPrice: json['cryptoPrice'],
      idLogo: json['idLogo'] ?? -1,
    );
  }

  // Convert a HistoryItem instance to a JSON Map
  Map<String, dynamic> toJson() {
    return {
      'crypto': crypto.toJson(),
      'balanceUsd': balanceUsd,
      'balanceCrypto': balanceCrypto,
      'cryptoTrendPercent': cryptoTrendPercent,
      'cryptoPrice': cryptoPrice,
      'idLogo': idLogo,
    };
  }
}
