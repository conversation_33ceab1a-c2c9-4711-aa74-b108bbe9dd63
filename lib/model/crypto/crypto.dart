import 'package:json_annotation/json_annotation.dart';
part 'crypto.g.dart';

enum CryptoType { native, token }

@JsonSerializable()
class Crypto {
  final String name;
  final String? chain;
  final String? icon;
  final int? chainId;
  final int? networkId;
  final NativeCurrency nativeCurrency;
  @JsonKey(name: 'rpc')
  final List<String>? rpcUrls;
  final String? contractAddress;
  final double? valueUsd;
  final bool canDisplay;
  final String? rpcValid;
  final String? infoURL;
  final String? shortName;
  final CryptoType type;
  Crypto({
    required this.nativeCurrency,
    required this.name,
    this.chain,
    this.networkId,
    this.icon,
    this.chainId,
    this.rpcUrls,
    this.contractAddress,
    this.canDisplay = false,
    this.valueUsd,
    this.rpcValid,
    this.infoURL,
    this.shortName,
    this.type = CryptoType.native,
  });
  factory Crypto.fromJson(Map<String, dynamic> json) => _$CryptoFromJson(json);

  Map<String, dynamic> toJson() => _$CryptoToJson(this);

  Crypto copyWith({
    String? name,
    String? icon,
    int? chainId,
    int? networkId,
    NativeCurrency? nativeCurrency,
    List<String>? rpcUrls,
    String? contractAddress,
    double? valueUsd,
    bool? canDisplay,
    String? symbol,
    String? rpcValid,
    String? infoURL,
    String? shortName,
    String? chain,
    CryptoType? type,
  }) {
    return Crypto(
      name: name ?? this.name,
      icon: icon ?? this.icon,
      chainId: chainId ?? this.chainId,
      networkId: networkId ?? this.networkId,
      nativeCurrency: nativeCurrency ?? this.nativeCurrency,
      rpcUrls: rpcUrls ?? this.rpcUrls,
      contractAddress: contractAddress ?? this.contractAddress,
      valueUsd: valueUsd ?? this.valueUsd,
      canDisplay: canDisplay ?? this.canDisplay,
      rpcValid: rpcValid ?? this.rpcValid,
      infoURL: infoURL ?? this.infoURL,
      shortName: shortName ?? this.shortName,
      chain: chain ?? this.chain,
      type: type ?? this.type,
    );
  }

  bool get isNative => type == CryptoType.native;

  String get symbol =>
      nativeCurrency?.symbol ?? contractAddress?.toUpperCase() ?? name;
  // String get getRpcUrl =>
  //     isNative
  //         ? (rpcUrls?.firstOrNull ?? "")
  //         : (network?.rpcUrls?.firstOrNull ?? "");
  // Crypto? get tokenNetwork => isNative ? this : network;
  // int get getChainId => (isNative ? chainId : network?.chainId) ?? 0;

  // String? get firstExplorer =>
  //     isNative ? explorers?.firstOrNull : network?.explorers?.firstOrNull;

  //String? get firstRpcUrl =>
  //  isNative ? rpcUrls?.firstOrNull : network?.rpcUrls?.firstOrNull;

  // static Crypto get nullToken => Crypto(
  //     name: "null",
  //     networkType: NetworkType.evm,
  //     color: Color(0),
  //     type: CryptoType.native,
  //     decimals: 0,
  //     cryptoId: "null",
  //     canDisplay: false,
  //     symbol: "Null",
  //     contractAddress: "",
  //     chainId: 0,
  //     rpcUrls: [""],
  //     explorers: [""]);
}

class Cryptos {
  final List<Crypto> networks;

  Cryptos({required this.networks});

  factory Cryptos.fromJson(List<dynamic> jsonList) {
    return Cryptos(
      networks: jsonList.map((json) => Crypto.fromJson(json)).toList(),
    );
  }

  List<Map<String, dynamic>> toJson() {
    return networks.map((network) => network.toJson()).toList();
  }
}

@JsonSerializable()
class NativeCurrency {
  final String name;
  final String symbol;
  final int? decimals;

  NativeCurrency({required this.name, required this.symbol, this.decimals});

  factory NativeCurrency.fromJson(Map<String, dynamic> json) =>
      _$NativeCurrencyFromJson(json);

  Map<String, dynamic> toJson() => _$NativeCurrencyToJson(this);

  NativeCurrency copyWith({String? name, String? symbol, int? decimals}) {
    return NativeCurrency(
      name: name ?? this.name,
      symbol: symbol ?? this.symbol,
      decimals: decimals ?? this.decimals,
    );
  }
}
