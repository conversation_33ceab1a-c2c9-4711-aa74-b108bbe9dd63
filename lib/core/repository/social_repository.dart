import 'dart:io';

import 'package:toii_social/core/service/social_service.dart';
import 'package:toii_social/model/base/base_response.dart';
import 'package:toii_social/model/comment/comment_model.dart';
import 'package:toii_social/model/follower/follower_model.dart';
import 'package:toii_social/model/media/media_model.dart';
import 'package:toii_social/model/post/hide_post_request.dart';
import 'package:toii_social/model/post/post_model.dart';
import 'package:toii_social/model/post/post_request.dart';
import 'package:toii_social/model/post/repost_request.dart';

abstract class SocialRepository {
  Future<BaseResponse<PostResponseDataModel>> getUserFeed(String? cursor, int limit);
  Future<BaseResponse<CommentListModel>> getCommentsOfPost(String id);
  Future<BaseResponse<CommentItemModel>> createCommentsOfPost(
    String id,
    CreateCommentRequestModel comment,
  );

  Future<BaseResponse<PostModel>> createNewPost(CreatePostRequestModel post);
  Future<BaseResponse<MediaUploadResponseModel>> uploadMedia(
    File file,
    String type,
  );
  Future<void> likePost(String id);
  Future<void> unlikePost(String id);
  Future<BaseResponse<PostModel>> getPostById(String id);
  Future<void> likeComment(String commentId);
  Future<void> unlikeComment(String commentId);
  Future<void> updatePost(String id, PostRequest postRequest);
  Future<void> deletePost(String id);
  Future<void> hidePost(String postId);
  Future<void> repostPost(RepostRequest request);

  Future<BaseResponse<PostResponseDataModel>> getUserPosts(String userId);
  Future<BaseResponse<PostResponseDataModel>> getUserReposts(String userId);


}

class SocialRepositoryImpl extends SocialRepository {
  final SocialService socialService;

  SocialRepositoryImpl({required this.socialService});

  @override
  Future<BaseResponse<PostResponseDataModel>> getUserFeed(String? cursor, int limit) {
    return socialService.getUserFeed(cursor, limit);
  }

  @override
  Future<BaseResponse<CommentListModel>> getCommentsOfPost(String id) {
    return socialService.getCommentsOfPost(id, true);
  }

  @override
  Future<BaseResponse<CommentItemModel>> createCommentsOfPost(
    String id,
    CreateCommentRequestModel comment,
  ) {
    return socialService.createCommentsOfPost(id, comment);
  }

  @override
  Future<BaseResponse<PostModel>> createNewPost(CreatePostRequestModel post) {
    return socialService.createPost(post);
  }

  @override
  Future<BaseResponse<MediaUploadResponseModel>> uploadMedia(
    File file,
    String type,
  ) async {
    return socialService.uploadMedia(file, type);
  }

  @override
  Future<void> likePost(String id) {
    return socialService.likePost(id, 'love');
  }

  @override
  Future<void> unlikePost(String id) {
    return socialService.unlikePost(id, 'love');
  }

  @override
  Future<BaseResponse<PostModel>> getPostById(String id) {
    return socialService.getPostById(id);
  }

  @override
  Future<void> likeComment(String commentId) {
    return socialService.likeComment(commentId, 'love');
  }

  @override
  Future<void> unlikeComment(String commentId) {
    return socialService.unlikeComment(commentId, 'love');
  }

  @override
  Future<void> updatePost(String id, PostRequest postRequest) {
    return socialService.updatePost(id, postRequest);
  }

  @override
  Future<void> deletePost(String id) {
    return socialService.deletePost(id);
  }

  @override
  Future<void> hidePost(String postId) {
    return socialService.hidePost(HidePostRequest(postId: postId));
  }

  @override
  Future<BaseResponse<PostResponseDataModel>> getUserPosts(String userId) {
    return socialService.getUserPosts(userId);
  }

  @override
  Future<BaseResponse<PostResponseDataModel>> getUserReposts(String userId) {
    return socialService.getUserReposts(userId);
  }

  @override
  Future<void> repostPost(RepostRequest request) {
    return socialService.repostPost(request);
  }


}
