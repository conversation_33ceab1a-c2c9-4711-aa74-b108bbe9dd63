import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:toii_social/model/base/base_response.dart';
import 'package:toii_social/model/follower/follower_model.dart';
import 'package:toii_social/model/user/user_model.dart';

part 'user_service.g.dart';

@RestApi()
abstract class UserService {
  factory UserService(Dio dio, {String baseUrl}) = _UserService;

  @GET('/user/api/v1/users/{user_id}/profile')
  Future<BaseResponse<UserModel>> getUserStats(@Path('user_id') String userId);

  @GET('/user/api/v1/follows/followers')
  Future<BaseResponse<FollowerListModel>> getFollowers();

  @GET('/user/api/v1/follows/following')
  Future<BaseResponse<FollowingListModel>> getFollowing();

  // Get following list for a user
  @GET('/user/api/v1/internal/following/{user_id}?limit=100')
  Future<BaseResponse<FollowingListModel>> getUserFollowing(
    @Path('user_id') String userId,
  );
}
