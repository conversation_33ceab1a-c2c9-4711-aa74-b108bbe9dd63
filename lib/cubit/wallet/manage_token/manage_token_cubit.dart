
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:toii_social/cubit/wallet/crypto/saved_crypto.dart';
import 'package:toii_social/model/crypto/crypto.dart';

part 'manage_token_state.dart';

class ManageTokenCubit extends Cubit<ManageTokenState> {
  ManageTokenCubit({required this.walletAddress}) : super(const ManageTokenState());
  final String walletAddress;
  void getData() async {
     final lists = await SavedCrypto.instance.getSavedCrypto(walletAddress);
  emit(state.copyWith(
      tokens: lists,
    ));
  }
  
}
